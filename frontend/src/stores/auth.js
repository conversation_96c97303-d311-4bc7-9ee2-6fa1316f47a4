/**
 * Pinia Authentication Store
 * Manages authentication state and actions
 */

import { defineStore } from 'pinia';
import authService from '@/services/auth.js';
import { transformUserFromBackend } from '@/utils/dataTransforms';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: authService.getUser(),
    isAuthenticated: authService.isAuthenticated(),
    loading: false,
    error: null
  }),

  getters: {
    /**
     * Get authentication token
     */
    token: () => authService.getToken(),

    /**
     * Get user's full name
     */
    userFullName: (state) => {
      if (!state.user) return '';
      const { firstName, lastName } = state.user;
      return [firstName, lastName].filter(Boolean).join(' ');
    },

    /**
     * Get user's role name
     */
    userRole: (state) => state.user?.role || state.user?.roleName || '',

    /**
     * Check if user is admin
     */
    isAdmin: (state) => {
      const role = (state.user?.role || state.user?.roleName || '').toLowerCase();
      return role === 'admin';
    },

    /**
     * Check if user is manager
     */
    isManager: (state) => {
      const role = (state.user?.role || state.user?.roleName || '').toLowerCase();
      return role === 'manager';
    },

    /**
     * Check if user is regular user
     */
    isUser: (state) => {
      const role = (state.user?.role || state.user?.roleName || '').toLowerCase();
      return role === 'user';
    },

    /**
     * Check if user is manager or admin
     */
    isManagerOrAdmin: (state) => {
      const role = (state.user?.role || state.user?.roleName || '').toLowerCase();
      return role === 'admin' || role === 'manager';
    }
  },

  actions: {
    /**
     * Login user
     */
    async login(email, password) {
      this.loading = true;
      this.error = null;

      try {
        const user = await authService.login(email, password);
        // Transform backend user data to frontend format
        this.user = transformUserFromBackend(user);
        this.isAuthenticated = true;
        return this.user;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Register new user
     */
    async register(userData) {
      this.loading = true;
      this.error = null;

      try {
        const user = await authService.register(userData);
        this.user = user;
        this.isAuthenticated = true;
        return user;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Logout user
     */
    async logout() {
      this.loading = true;

      try {
        await authService.logout();
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        this.user = null;
        this.isAuthenticated = false;
        this.loading = false;
        this.error = null;
      }
    },

    /**
     * Refresh user data
     */
    async refreshUser() {
      if (!this.isAuthenticated) return;

      try {
        const user = await authService.getCurrentUser();
        this.user = user;
      } catch (error) {
        console.error('Failed to refresh user:', error);
        // If refresh fails, logout user
        await this.logout();
      }
    },

    /**
     * Verify authentication status
     */
    async verifyAuth() {
      if (!authService.isAuthenticated()) {
        this.user = null;
        this.isAuthenticated = false;
        return false;
      }

      try {
        const isValid = await authService.validateSession();
        if (!isValid) {
          await this.logout();
          return false;
        }

        // Update user data from auth service
        this.user = authService.getUser();
        this.isAuthenticated = true;
        return true;
      } catch (error) {
        console.error('Auth verification error:', error);
        await this.logout();
        return false;
      }
    },

    /**
     * Initialize auth state on app start
     */
    async initialize() {
      this.loading = true;

      try {
        if (authService.isAuthenticated()) {
          const isValid = await this.verifyAuth();
          if (isValid) {
            this.user = authService.getUser();
            this.isAuthenticated = true;
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        await this.logout();
      } finally {
        this.loading = false;
      }
    },

    /**
     * Clear error state
     */
    clearError() {
      this.error = null;
    },

    /**
     * Check if user has required role
     */
    hasRole(requiredRole) {
      return authService.hasRole(requiredRole);
    },

    /**
     * Handle authentication errors
     */
    handleAuthError(error) {
      const authError = authService.handleAuthError(error);
      this.error = authError.message;

      // Handle specific error types
      if (error.response?.data?.code === 'TOKEN_BLACKLISTED' ||
          error.response?.data?.code === 'TOKEN_EXPIRED') {
        this.logout();
      }

      return authError;
    },

    /**
     * Force logout and redirect
     */
    forceLogout() {
      authService.forceLogout();
      this.user = null;
      this.isAuthenticated = false;
      this.error = null;
    },

    /**
     * Check if user can perform action based on role
     */
    canPerformAction(action, resource = null) {
      if (!this.isAuthenticated) return false;

      const userRole = this.userRole.toLowerCase();

      // Admin can do everything
      if (userRole === 'admin') return true;

      // Define role-based permissions
      const permissions = {
        manager: {
          users: ['read'],
          tasks: ['create', 'read', 'update', 'delete', 'assign'],
          dashboard: ['read']
        },
        user: {
          tasks: ['read', 'update_status'],
          dashboard: ['read'],
          profile: ['read', 'update']
        }
      };

      const rolePermissions = permissions[userRole];
      if (!rolePermissions) return false;

      if (resource) {
        return rolePermissions[resource]?.includes(action) || false;
      }

      // Check if action is allowed for any resource
      return Object.values(rolePermissions).some(actions => actions.includes(action));
    }
  }
});
